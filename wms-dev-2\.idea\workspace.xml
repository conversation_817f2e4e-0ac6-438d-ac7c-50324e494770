<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6b8780f7-32c1-4c6b-8367-dff8f42bf34f" name="更改" comment="fix:优化辅料移库及辅料入库容器选择逻辑">
      <change beforePath="$PROJECT_DIR$/wms-common/src/main/java/com/tgvs/wms/common/constant/CommonConstant.java" beforeDir="false" afterPath="$PROJECT_DIR$/wms-common/src/main/java/com/tgvs/wms/common/constant/CommonConstant.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wms-common/src/main/java/com/tgvs/wms/common/constant/Result.java" beforeDir="false" afterPath="$PROJECT_DIR$/wms-common/src/main/java/com/tgvs/wms/common/constant/Result.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wms-web/src/main/resources/application.properties" beforeDir="false" afterPath="$PROJECT_DIR$/wms-web/src/main/resources/application.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wms-web/src/main/webapp/pages/business/relocation/planMaterial/planMaterialList.html" beforeDir="false" afterPath="$PROJECT_DIR$/wms-web/src/main/webapp/pages/business/relocation/planMaterial/planMaterialList.html" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <excluded-from-favorite>
      <branch-storage>
        <map>
          <entry type="REMOTE">
            <value>
              <list>
                <branch-info repo="$PROJECT_DIR$/.." source="origin/master" />
              </list>
            </value>
          </entry>
        </map>
      </branch-storage>
    </excluded-from-favorite>
    <favorite-branches>
      <branch-storage>
        <map>
          <entry type="REMOTE">
            <value>
              <list>
                <branch-info repo="$PROJECT_DIR$/.." source="origin/dev" />
              </list>
            </value>
          </entry>
        </map>
      </branch-storage>
    </favorite-branches>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\开发环境\maven_repository" />
        <option name="userSettingsFile" value="D:\Program Files (x86)\java\apache-maven-3.9.9-bin\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="30D5pDmu8VWRlxAVStDmELohuhk" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.wms [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.wms [package].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Notification.DisplayName-DoNotAsk-DatabaseConfigFileWatcher.found&quot;: &quot;找到数据库连接形参&quot;,
    &quot;Notification.DoNotAsk-DatabaseConfigFileWatcher.found&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.ErpTransactionApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.WebApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/开发代码/Code/WMS3/wms-dev-2&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.WebApplication">
    <configuration name="AgvTransactionApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="wms-transaction-agv" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.tgvs.wms.AgvTransactionApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ErpTransactionApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="wms-transaction-erp" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.tgvs.wms.ErpTransactionApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LiftTransactionApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="wms-transaction-lift" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.tgvs.wms.LiftTransactionApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MesTransactionApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="wms-transaction-mes" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.tgvs.wms.MesTransactionApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MfcTransactionApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="wms-transaction-mfc" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.tgvs.wms.MfcTransactionApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SocketApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="wms-socket" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.tgvs.wms.socket.SocketApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TransactionAbbApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="wms-transaction-abb" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.tgvs.wms.TransactionAbbApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TransactionConvApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="wms-transaction-conv" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.tgvs.wms.TransactionConvApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WebApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="wms-web" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.tgvs.wms.WebApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="6b8780f7-32c1-4c6b-8367-dff8f42bf34f" name="更改" comment="" />
      <created>1753149484210</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753149484210</updated>
      <workItem from="1753149485958" duration="8860000" />
      <workItem from="1753170998425" duration="4119000" />
      <workItem from="1753437276262" duration="510000" />
      <workItem from="1753499340140" duration="427000" />
      <workItem from="1753499803290" duration="19352000" />
      <workItem from="1753662748113" duration="26516000" />
      <workItem from="1753705659359" duration="4676000" />
      <workItem from="1753747694735" duration="27640000" />
      <workItem from="1753799914040" duration="779000" />
      <workItem from="1753835258103" duration="16835000" />
      <workItem from="1753869405188" duration="314000" />
      <workItem from="1753869743457" duration="187000" />
      <workItem from="1753870206506" duration="3516000" />
      <workItem from="1753875886018" duration="9868000" />
      <workItem from="1753920829256" duration="2478000" />
    </task>
    <task id="LOCAL-00001" summary="fix:修复1格箱子分配容器">
      <option name="closed" value="true" />
      <created>1753163708599</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753163708599</updated>
    </task>
    <task id="LOCAL-00002" summary="fix:修复盘点单">
      <option name="closed" value="true" />
      <created>1753510569123</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753510569123</updated>
    </task>
    <task id="LOCAL-00003" summary="fix:修改差异盘点单逻辑">
      <option name="closed" value="true" />
      <created>1753519507926</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753519507926</updated>
    </task>
    <task id="LOCAL-00004" summary="fix:修改差异盘点单逻辑">
      <option name="closed" value="true" />
      <created>1753519534215</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753519534215</updated>
    </task>
    <task id="LOCAL-00005" summary="fix:优化查询">
      <option name="closed" value="true" />
      <created>1753666687883</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753666687883</updated>
    </task>
    <task id="LOCAL-00006" summary="fix:新增盘点入库">
      <option name="closed" value="true" />
      <created>1753666707105</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753666707105</updated>
    </task>
    <task id="LOCAL-00007" summary="fix:修改差异盘点分页">
      <option name="closed" value="true" />
      <created>1753670916022</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753670916022</updated>
    </task>
    <task id="LOCAL-00008" summary="fix:修改调拨单接口">
      <option name="closed" value="true" />
      <created>1753697241215</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753697241215</updated>
    </task>
    <task id="LOCAL-00009" summary="fix:修改辅料出库多物料同容器出库逻辑">
      <option name="closed" value="true" />
      <created>1753697302972</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753697302972</updated>
    </task>
    <task id="LOCAL-00010" summary="fix:修改辅料出库多物料同容器出库逻辑">
      <option name="closed" value="true" />
      <created>1753699368374</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753699368374</updated>
    </task>
    <task id="LOCAL-00011" summary="fix:新增辅料相关逻辑">
      <option name="closed" value="true" />
      <created>1753710081521</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753710081521</updated>
    </task>
    <task id="LOCAL-00012" summary="fix:修改辅料预出库状态">
      <option name="closed" value="true" />
      <created>1753751588900</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753751588900</updated>
    </task>
    <task id="LOCAL-00013" summary="fix:优化PDA接口">
      <option name="closed" value="true" />
      <created>1753771581163</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1753771581163</updated>
    </task>
    <task id="LOCAL-00014" summary="fix:优化PDA接口">
      <option name="closed" value="true" />
      <created>1753780638344</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1753780638345</updated>
    </task>
    <task id="LOCAL-00015" summary="fix:优化辅料出库逻辑">
      <option name="closed" value="true" />
      <created>1753859439869</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1753859439869</updated>
    </task>
    <task id="LOCAL-00016" summary="fix:优化辅料差异判断服务">
      <option name="closed" value="true" />
      <created>1753870932082</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1753870932082</updated>
    </task>
    <task id="LOCAL-00017" summary="fix:优化辅料移库及辅料入库容器选择逻辑">
      <option name="closed" value="true" />
      <created>1753921083868</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1753921083868</updated>
    </task>
    <option name="localTasksCounter" value="18" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="dev" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix:修复1格箱子分配容器" />
    <MESSAGE value="fix:修复盘点单" />
    <MESSAGE value="fix:修改差异盘点单逻辑" />
    <MESSAGE value="fix:优化查询" />
    <MESSAGE value="fix:新增盘点入库" />
    <MESSAGE value="fix:修改差异盘点分页" />
    <MESSAGE value="fix:修改调拨单接口" />
    <MESSAGE value="fix:修改辅料出库多物料同容器出库逻辑" />
    <MESSAGE value="fix:新增辅料相关逻辑" />
    <MESSAGE value="fix:修改辅料预出库状态" />
    <MESSAGE value="fix:优化PDA接口" />
    <MESSAGE value="fix:优化辅料出库逻辑" />
    <MESSAGE value="fix:优化辅料差异判断服务" />
    <MESSAGE value="fix:优化辅料移库及辅料入库容器选择逻辑" />
    <option name="LAST_COMMIT_MESSAGE" value="fix:优化辅料移库及辅料入库容器选择逻辑" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/machineAuxiliary/service/impl/AuxiliaryInboundServiceImpl.java</url>
          <line>974</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/machineAuxiliary/service/impl/AuxiliaryInboundServiceImpl.java</url>
          <line>987</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/machineAuxiliary/service/impl/AuxiliaryPreboxServiceImpl.java</url>
          <line>700</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/machineAuxiliary/service/impl/AuxiliaryPreboxServiceImpl.java</url>
          <line>1035</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/machineAuxiliary/service/impl/AuxiliaryPreboxServiceImpl.java</url>
          <line>1078</line>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/machineAuxiliary/service/impl/AuxiliaryPreboxServiceImpl.java</url>
          <line>1070</line>
          <option name="timeStamp" value="15" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/machineAuxiliary/service/impl/AuxiliaryPreboxServiceImpl.java</url>
          <line>1074</line>
          <option name="timeStamp" value="16" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-transaction/wms-transaction-erp/src/main/java/com/tgvs/wms/transaction/erp/service/ErpInventoryService.java</url>
          <line>211</line>
          <option name="timeStamp" value="21" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/machineAuxiliary/service/impl/AuxiliaryTransferServiceImpl.java</url>
          <line>64</line>
          <option name="timeStamp" value="31" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/machineAuxiliary/service/impl/AuxiliaryTransferServiceImpl.java</url>
          <line>150</line>
          <option name="timeStamp" value="32" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/machineAuxiliary/service/impl/AuxiliaryOutboundServiceImpl.java</url>
          <line>1881</line>
          <option name="timeStamp" value="61" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/machineAuxiliary/service/impl/ContainerStatusManager.java</url>
          <line>152</line>
          <option name="timeStamp" value="69" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/machineAuxiliary/service/impl/PreboxRecordManager.java</url>
          <line>390</line>
          <option name="timeStamp" value="79" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/machineAuxiliary/service/impl/PreboxRecordManager.java</url>
          <line>405</line>
          <option name="timeStamp" value="80" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/machineAuxiliary/service/impl/PreboxRecordManager.java</url>
          <line>606</line>
          <option name="timeStamp" value="81" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-web/src/main/java/com/tgvs/wms/api/controller/PDA/PDAController.java</url>
          <line>128</line>
          <option name="timeStamp" value="82" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-web/src/main/java/com/tgvs/wms/api/controller/PDA/PDAController.java</url>
          <line>124</line>
          <option name="timeStamp" value="83" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/task/service/impl/RosDataIntegrationService.java</url>
          <line>235</line>
          <option name="timeStamp" value="85" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/task/service/impl/RosDataIntegrationService.java</url>
          <line>230</line>
          <option name="timeStamp" value="86" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/task/service/impl/RosDataIntegrationService.java</url>
          <line>214</line>
          <option name="timeStamp" value="87" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/task/service/impl/RosDataIntegrationService.java</url>
          <line>40</line>
          <option name="timeStamp" value="88" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/task/service/impl/RosDataIntegrationService.java</url>
          <line>38</line>
          <option name="timeStamp" value="89" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/task/controller/ManualReportController.java</url>
          <line>382</line>
          <option name="timeStamp" value="90" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/task/service/impl/RosReportServiceImpl.java</url>
          <line>961</line>
          <option name="timeStamp" value="91" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/task/service/impl/RosDataIntegrationService.java</url>
          <line>206</line>
          <option name="timeStamp" value="97" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/wms-service/wms-service-business/src/main/java/com/tgvs/wms/business/modules/production/controller/ProductionPlanController.java</url>
          <line>99</line>
          <option name="timeStamp" value="100" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>